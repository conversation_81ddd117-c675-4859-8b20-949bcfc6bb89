import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../providers/auth_provider.dart';
import '../providers/domain_provider.dart';
import '../providers/category_provider.dart';
import '../utils/theme.dart';
import '../widgets/enhanced_card.dart';
import '../widgets/enhanced_loading.dart';
import '../widgets/empty_state.dart';
import 'add_domain_screen.dart';
import 'add_category_screen.dart';
import 'add_simple_domain_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    // Defer loading until after build is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    final domainProvider = Provider.of<DomainProvider>(context, listen: false);
    final categoryProvider = Provider.of<CategoryProvider>(
      context,
      listen: false,
    );

    await Future.wait([
      domainProvider.loadDashboardStats(),
      categoryProvider.loadCategories(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<DomainProvider>(
        builder: (context, domainProvider, child) {
          if (domainProvider.isLoading) {
            return const Center(
              child: EnhancedLoading.circular(message: 'Loading dashboard...'),
            );
          }

          if (domainProvider.error != null) {
            return EmptyState.error(
              title: 'Dashboard Error',
              description: domainProvider.error!,
              actionText: 'Retry',
              onAction: _loadData,
            );
          }

          final stats = domainProvider.dashboardStats;

          return RefreshIndicator(
            onRefresh: _loadData,
            child: LayoutBuilder(
              builder: (context, constraints) {
                final isSmallScreen = constraints.maxHeight < 600;
                return SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: EdgeInsets.all(
                    isSmallScreen ? AppTheme.spaceSM : AppTheme.spaceMD,
                  ),
                  child: AnimationLimiter(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: AnimationConfiguration.toStaggeredList(
                        duration: const Duration(milliseconds: 375),
                        childAnimationBuilder: (widget) => SlideAnimation(
                          horizontalOffset: 50.0,
                          child: FadeInAnimation(child: widget),
                        ),
                        children: [
                          // Welcome Header
                          _buildWelcomeHeader(),
                          SizedBox(
                            height: _isLargeScreen()
                                ? AppTheme.spaceXL
                                : AppTheme.spaceLG,
                          ),

                          // Quick Stats Section
                          _buildQuickStats(stats),
                          SizedBox(
                            height: _isLargeScreen()
                                ? AppTheme.spaceXL
                                : AppTheme.spaceLG,
                          ),

                          // Categories Overview Section
                          _buildCategoriesOverview(),
                          SizedBox(
                            height: _isLargeScreen()
                                ? AppTheme.spaceXL
                                : AppTheme.spaceLG,
                          ),

                          // Quick Actions Section
                          _buildQuickActions(),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildWelcomeHeader() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return EnhancedCard.gradient(
          gradient: AppTheme.primaryGradient,
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    authProvider.admin?.name.substring(0, 1).toUpperCase() ??
                        'A',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppTheme.spaceMD),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back!',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spaceXS),
                    Text(
                      authProvider.admin?.name ?? 'Admin',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spaceXS),
                    Text(
                      'Here\'s your domain portfolio overview',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 12,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.all(AppTheme.spaceSM),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: AppTheme.smallRadius,
                ),
                child: const Icon(
                  Icons.dashboard_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickStats(Map<String, dynamic>? stats) {
    if (stats == null) {
      return EnhancedCard.outlined(
        child: const Padding(
          padding: EdgeInsets.all(AppTheme.spaceXL),
          child: Center(
            child: Text(
              'No statistics available',
              style: TextStyle(
                color: AppTheme.textSecondaryLight,
                fontSize: 16,
              ),
            ),
          ),
        ),
      );
    }

    final statItems = [
      _StatItem(
        title: 'Total Domains',
        value: stats['total_domains']?.toString() ?? '0',
        icon: Icons.domain_rounded,
        gradient: AppTheme.primaryGradient,
      ),
      _StatItem(
        title: 'Total Reserved',
        value: stats['total_simple_domains']?.toString() ?? '0',
        icon: Icons.bookmark_rounded,
        gradient: AppTheme.successGradient,
      ),
      _StatItem(
        title: 'Categories',
        value: stats['total_categories']?.toString() ?? '0',
        icon: Icons.category_rounded,
        gradient: AppTheme.secondaryGradient,
      ),
      _StatItem(
        title: 'Expiring Soon',
        value: stats['expiring_soon']?.toString() ?? '0',
        icon: Icons.warning_rounded,
        gradient: AppTheme.warningGradient,
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Statistics',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimaryLight,
          ),
        ),
        const SizedBox(height: AppTheme.spaceMD),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: _getStatsGridCrossAxisCount(),
            crossAxisSpacing: AppTheme.spaceMD,
            mainAxisSpacing: AppTheme.spaceMD,
            childAspectRatio: _getStatsChildAspectRatio(),
          ),
          itemCount: statItems.length,
          itemBuilder: (context, index) {
            final item = statItems[index];
            return StatsCard(
                  title: item.title,
                  value: item.value,
                  icon: item.icon,
                  gradient: item.gradient,
                )
                .animate(delay: (index * 100).ms)
                .fadeIn()
                .slideY(begin: 0.3, end: 0);
          },
        ),
      ],
    );
  }

  Widget _buildCategoriesOverview() {
    return Consumer<CategoryProvider>(
      builder: (context, categoryProvider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Categories Overview',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w700,
                color: AppTheme.textPrimaryLight,
              ),
            ),
            const SizedBox(height: AppTheme.spaceMD),
            EnhancedCard.outlined(
              child: categoryProvider.isLoading
                  ? const Padding(
                      padding: EdgeInsets.all(AppTheme.spaceLG),
                      child: Center(
                        child: EnhancedLoading.circular(
                          message: 'Loading categories...',
                        ),
                      ),
                    )
                  : categoryProvider.categories.isEmpty
                  ? const Padding(
                      padding: EdgeInsets.all(AppTheme.spaceLG),
                      child: Center(
                        child: Text(
                          'No categories found',
                          style: TextStyle(color: AppTheme.textSecondaryLight),
                        ),
                      ),
                    )
                  : Column(
                      children: [
                        // Table Header
                        Container(
                          padding: const EdgeInsets.all(AppTheme.spaceMD),
                          decoration: BoxDecoration(
                            color: Theme.of(
                              context,
                            ).colorScheme.surfaceContainerHighest,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(12),
                              topRight: Radius.circular(12),
                            ),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                flex: _getCategoriesTableFlex(),
                                child: Text(
                                  'Category',
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: _getCategoriesTableFontSize(),
                                    color: AppTheme.textPrimaryLight,
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: Text(
                                  'Purchase',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: _getCategoriesTableFontSize(),
                                    color: AppTheme.textPrimaryLight,
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: Text(
                                  'Reserved',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: _getCategoriesTableFontSize(),
                                    color: AppTheme.textPrimaryLight,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Table Rows
                        ...categoryProvider.categories.map(
                          (category) => _buildCategoryRow(category),
                        ),
                        // Total Row
                        _buildTotalRow(categoryProvider.categories),
                      ],
                    ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCategoryRow(category) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spaceLG, // More horizontal padding for S24 Ultra
        vertical: AppTheme.spaceMD,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: _getCategoriesTableFlex(),
            child: Row(
              children: [
                Container(
                  width: _isLargeScreen() ? 14 : 12,
                  height: _isLargeScreen() ? 14 : 12,
                  decoration: BoxDecoration(
                    color: Color(
                      int.parse(category.color.replaceFirst('#', '0xFF')),
                    ),
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(
                  width: _isLargeScreen() ? AppTheme.spaceMD : AppTheme.spaceSM,
                ),
                Expanded(
                  child: Text(
                    category.name,
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: _getCategoriesRowFontSize(),
                      color: AppTheme.textPrimaryLight,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              category.buyDomainsCount.toString(),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: _getCategoriesNumberFontSize(),
                color: AppTheme.primaryLight,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              category.reserveDomainsCount.toString(),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: _getCategoriesNumberFontSize(),
                color: AppTheme.successLight,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalRow(List<dynamic> categories) {
    // Calculate totals
    int totalPurchase = categories.fold<int>(
      0,
      (sum, category) => sum + (category.buyDomainsCount as int? ?? 0),
    );
    int totalReserved = categories.fold<int>(
      0,
      (sum, category) => sum + (category.reserveDomainsCount as int? ?? 0),
    );

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spaceLG,
        vertical: AppTheme.spaceMD,
      ),
      decoration: BoxDecoration(
        color: Theme.of(
          context,
        ).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: _getCategoriesTableFlex(),
            child: Row(
              children: [
                Container(
                  width: _isLargeScreen() ? 14 : 12,
                  height: _isLargeScreen() ? 14 : 12,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(
                  width: _isLargeScreen() ? AppTheme.spaceMD : AppTheme.spaceSM,
                ),
                Expanded(
                  child: Text(
                    'Total',
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                      fontSize: _getCategoriesTableFontSize(),
                      color: AppTheme.textPrimaryLight,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              totalPurchase.toString(),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: _isLargeScreen() ? 17 : 15,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              totalReserved.toString(),
              textAlign: TextAlign.center,
              style: TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: _isLargeScreen() ? 17 : 15,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimaryLight,
          ),
        ),
        SizedBox(
          height: _isLargeScreen() ? AppTheme.spaceMD : AppTheme.spaceSM,
        ),
        Column(
          children: [
            // First row with Add Domain and Add Reserved
            Row(
              children: [
                Expanded(
                  child: _buildActionCard(
                    'Add Domain',
                    'Register a new domain',
                    Icons.add_rounded,
                    AppTheme.primaryGradient,
                    () async {
                      await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AddDomainScreen(),
                        ),
                      );
                      // Refresh dashboard data when returning
                      _loadData();
                    },
                  ),
                ),
                SizedBox(
                  width: _isLargeScreen() ? AppTheme.spaceMD : AppTheme.spaceSM,
                ),
                Expanded(
                  child: _buildActionCard(
                    'Add Reserved',
                    'Reserve a domain',
                    Icons.bookmark_add_rounded,
                    AppTheme.successGradient,
                    () async {
                      await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AddSimpleDomainScreen(),
                        ),
                      );
                      // Refresh dashboard data when returning
                      _loadData();
                    },
                  ),
                ),
              ],
            ),
            SizedBox(
              height: _isLargeScreen() ? AppTheme.spaceMD : AppTheme.spaceSM,
            ),
            // Second row with Add Category (centered)
            Row(
              children: [
                Expanded(
                  child: _buildActionCard(
                    'Add Category',
                    'Create new category',
                    Icons.category_rounded,
                    AppTheme.secondaryGradient,
                    () async {
                      await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AddCategoryScreen(),
                        ),
                      );
                      // Refresh dashboard data when returning
                      _loadData();
                    },
                  ),
                ),
                const SizedBox(width: AppTheme.spaceMD),
                // Empty space to balance the layout
                const Expanded(child: SizedBox()),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    String subtitle,
    IconData icon,
    LinearGradient gradient,
    VoidCallback onTap,
  ) {
    return EnhancedCard.gradient(
      gradient: gradient,
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spaceMD),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: AppTheme.smallRadius,
            ),
            child: Icon(icon, color: Colors.white, size: 32),
          ),
          const SizedBox(height: AppTheme.spaceMD),
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: AppTheme.spaceXS),
          Text(
            subtitle,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.8),
              fontSize: 12,
              fontWeight: FontWeight.w400,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Responsive helper methods
  bool _isLargeScreen() {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    // Samsung S24 Ultra has approximately 412x915 logical pixels
    // Consider screens with width > 400 and height > 850 as large screens
    return screenWidth > 400 && screenHeight > 850;
  }

  int _getStatsGridCrossAxisCount() {
    return _isLargeScreen()
        ? 2
        : 2; // Keep 2 columns for both, but adjust spacing
  }

  double _getStatsChildAspectRatio() {
    return _isLargeScreen() ? 1.8 : 1.5; // More height for smaller screens
  }

  int _getCategoriesTableFlex() {
    return _isLargeScreen()
        ? 4
        : 3; // Less space for category names on small screens
  }

  double _getCategoriesTableFontSize() {
    return _isLargeScreen() ? 16.0 : 14.0;
  }

  double _getCategoriesRowFontSize() {
    return _isLargeScreen() ? 15.0 : 13.0;
  }

  double _getCategoriesNumberFontSize() {
    return _isLargeScreen() ? 16.0 : 14.0;
  }
}

class _StatItem {
  final String title;
  final String value;
  final IconData icon;
  final LinearGradient gradient;

  const _StatItem({
    required this.title,
    required this.value,
    required this.icon,
    required this.gradient,
  });
}
